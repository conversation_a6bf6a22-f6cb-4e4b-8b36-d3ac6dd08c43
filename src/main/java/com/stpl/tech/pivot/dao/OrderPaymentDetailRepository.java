package com.stpl.tech.pivot.dao;

import com.stpl.tech.pivot.data.model.OrderPaymentDetailEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrderPaymentDetailRepository extends JpaRepository<OrderPaymentDetailEntity, Integer> {
    OrderPaymentDetailEntity findByExternalOrderIdAndOrderIdNullAndRequestStatusNot(String externalOrderId, String requestStatus);

    List<OrderPaymentDetailEntity> findByExternalOrderIdAndRequestStatusNot(String merchantTransactionId, String name);

    OrderPaymentDetailEntity findByExternalOrderId(String merchantTransactionId);
}
