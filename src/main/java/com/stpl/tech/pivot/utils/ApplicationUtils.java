package com.stpl.tech.pivot.utils;

import com.google.gson.Gson;
import com.stpl.tech.pivot.exceptions.BaseException;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.joda.time.format.DateTimeFormatter;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

public class ApplicationUtils extends AppUtils {
    public ApplicationUtils() {
        // TODO document why this constructor is empty
    }

    public static String generateRandomString() {
        try {
            SecureRandom secureRandom = SecureRandom.getInstanceStrong();
            byte[] randomBytes = new byte[8];
            secureRandom.nextBytes(randomBytes);
            byte[] encryptedBytes = MessageDigest.getInstance("SHA-256").digest(randomBytes);
            String base64Encoded = Base64.getEncoder().encodeToString(encryptedBytes);
            return base64Encoded.substring(0, 4);
        } catch (NoSuchAlgorithmException e) {
            throw new BaseException("Error generating random string", e);
        }
    }

    public static <T> T parseResponse(String response, Class<T> c) {
        Gson gson = new Gson();
        return gson.fromJson(response, c);
    }

    public static String getCurrentTimeISTString() {
        return AppUtils.getCurrentTimeIST().toString(AppConstants.DATE_TIME_WITH_NO_MILISECOND_FORMATTER);
    }

    public static String getCurrentTimeISTString(DateTimeFormatter format) {
        return AppUtils.getCurrentTimeIST().toString(format);
    }


    public static String getGeneratePartnerExternalOrderId(String module, DateTimeFormatter format) {
        return module+ getCurrentTimeISTString(format) +  generateRandomString().toUpperCase();
    }

    public static void main(String[] args) {
        System.out.println(getGeneratePartnerExternalOrderId(ApplicationConstant.KETTLE_ORDER, AppConstants.DATE_TIME_FORMATTER_WITH_NO_CHARACTERS));
    }
}
