package com.stpl.tech.pivot.core.payment.factory;


import com.stpl.tech.pivot.core.payment.PaymentAdapter;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.core.payment.adapter.PayPhiEDCAdapter;
import com.stpl.tech.pivot.core.payment.adapter.PaytmEDCAdapter;
import com.stpl.tech.pivot.core.payment.adapter.RazorPayAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PaymentFactory {

    @Autowired
    private RazorPayAdapter razorPayAdapter;
    @Autowired
    private PaytmEDCAdapter paytmEDCAdapter;

    @Autowired
    private PayPhiEDCAdapter payPhiEDCAdapter;


    public PaymentAdapter getCreatePaymentAdapter(PaymentPartnerType paymentPartnerType) {
        switch (paymentPartnerType) {
            case RAZORPAY: {
                return razorPayAdapter;
            }
            case PATYM_EDC: {
                return paytmEDCAdapter;
            }
            case PAYPHI_EDC: {
                return payPhiEDCAdapter;
            }
            default: {
                throw new IllegalArgumentException("Payment partner " + paymentPartnerType.name() + " not found");
            }
        }
    }
}
