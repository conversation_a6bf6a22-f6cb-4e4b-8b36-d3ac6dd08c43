package com.stpl.tech.pivot.domain;

import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.pivot.utils.ApplicationUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.TreeMap;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PayPhiEdcCreateRequest implements PaymentRequest{

    private String storeCode;
    private String aggregatorId;
    private  String posAppId;
    private String posTillNo;
    private String referenceNo;
    private String desc;
    private String chargeAmount;
    private String currencyCode;
    private String callbackURL;
    private PayPhiEdcCreateResponse payPhiEdcCreateResponse;

    @Override
    public String getPartnerOrderId() {
        return null;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {

        return null;
    }

    @Override
    public String getStatus() {
        return null;
    }

}
