package com.stpl.tech.pivot.service.impl;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRequestStatus;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.core.payment.factory.PaymentFactory;
import com.stpl.tech.pivot.dao.OrderPaymentDetailRepository;
import com.stpl.tech.pivot.data.model.OrderPaymentDetailEntity;
import com.stpl.tech.pivot.domain.OrderPaymentDetail;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateRequest;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateResponse;
import com.stpl.tech.pivot.domain.PayPhiStatusRequest;
import com.stpl.tech.pivot.domain.PaytmEDCStatusResponse;
import com.stpl.tech.pivot.domain.PaytmEdcCreateRequest;
import com.stpl.tech.pivot.domain.PaytmEdcStatusRequest;
import com.stpl.tech.pivot.domain.mapper.OrderPaymentDetailMapper;
import com.stpl.tech.pivot.exceptions.BaseException;
import com.stpl.tech.pivot.service.PaymentService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Objects;

@Service
@Log4j2
public class PaymentServiceImpl implements PaymentService {
    @Autowired
    private PaymentFactory paymentFactory;
    @Autowired
    private OrderPaymentDetailRepository orderPaymentDetailRepository;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PaytmEdcCreateRequest initiateTransactionRequest(OrderPaymentRequest order, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PaytmEdcCreateRequest) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .createPaymentRequest(order, new HashMap<>());

    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PayPhiEdcCreateRequest initiatePayphiTransactionRequest(OrderPaymentRequest order, PaymentPartnerType paymentPartnerType) throws Exception{
        return (PayPhiEdcCreateRequest) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .createPaymentRequest(order, new HashMap<>());
    }

    /**
     * @param transactionId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = true, propagation = Propagation.REQUIRED)
    public OrderPaymentDetail validateOrderPaymentDetail(String transactionId) {
        OrderPaymentDetailEntity orderPaymentDetail = orderPaymentDetailRepository.findByExternalOrderIdAndOrderIdNullAndRequestStatusNot(transactionId,
                PaymentRequestStatus.CANCELLED.name());
        if (Objects.isNull(orderPaymentDetail)) {
            throw new BaseException("Order Payment Not Found For the Transaction Id");
        }
        return OrderPaymentDetailMapper.INSTANCE.toDomain(orderPaymentDetail);
    }

    @Override
    public PaytmEDCStatusResponse updateTransactionRequest(PaytmEdcStatusRequest request, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PaytmEDCStatusResponse) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .updatePayment(request, true);
    }

    @Override
    public PayPhiStatusRequest updatePayPhiTransactionRequest(PayPhiStatusRequest request, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PayPhiStatusRequest) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .updatePayment(request, true);
    }
}
