package com.stpl.tech.pivot.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stpl.tech.kettle.delivery.model.PaymentPartner;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRequestStatus;
import com.stpl.tech.pivot.dao.OrderPaymentDetailRepository;
import com.stpl.tech.pivot.data.model.OrderPaymentDetailEntity;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateRequest;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateResponse;
import com.stpl.tech.pivot.domain.PayPhiStatusRequest;
import com.stpl.tech.pivot.domain.PayPhiStatusResponse;
import com.stpl.tech.pivot.domain.PaytmEDCTransactionResponse;
import com.stpl.tech.pivot.exceptions.BaseException;
import com.stpl.tech.pivot.properties.PayPhiProperties;
import com.stpl.tech.pivot.properties.PaytmProperties;
import com.stpl.tech.pivot.service.PayphiPaymentService;
import com.stpl.tech.pivot.service.WebClientService;
import com.stpl.tech.pivot.utils.ApplicationConstant;
import com.stpl.tech.pivot.utils.ApplicationUtils;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.s3.endpoints.internal.Value;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.net.ssl.HttpsURLConnection;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URL;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Log4j2
public class PayphiPaymentServiceImpl  implements PayphiPaymentService {

    @Autowired
    private WebClientService webClientService;

    @Autowired
    private PayPhiProperties payphiProperties;

    @Autowired
    private OrderPaymentDetailRepository orderPaymentDetailRepository;

    @Override
    public PayPhiEdcCreateRequest initiatePayphiEdcTransaction(OrderPaymentRequest orderPaymentRequest) throws Exception{
        PayPhiEdcCreateRequest request = createRequest(orderPaymentRequest);
        String msg =createPayPhiEdcRequest(orderPaymentRequest);
        String hashKey = hmacDigest(msg, payphiProperties.getSecretKey());
        String response = webClientService.postRequestPayPhi(payphiProperties.getSaleApi(),msg, hashKey);
        log.info("Prininting value of payphi response::{}",response);
        PayPhiEdcCreateResponse payPhiEdcCreateResponse = ApplicationUtils.parseResponse(response, PayPhiEdcCreateResponse.class);
        if(Objects.isNull(payPhiEdcCreateResponse)){
            throw new BaseException("Server Not Responding");
        }
        //TODO check of response status code and create not equal to check
        else if(Objects.nonNull(payPhiEdcCreateResponse.getResponseCode()) && !payPhiEdcCreateResponse.getResponseCode().equalsIgnoreCase("0000")){
            throw new BaseException(payPhiEdcCreateResponse.getRespDescription());
        }
        else{
            getOrderPaymentDetail(orderPaymentRequest,payPhiEdcCreateResponse);
            request.setReferenceNo(payPhiEdcCreateResponse.getReferenceNo());
            request.setPayPhiEdcCreateResponse(payPhiEdcCreateResponse);
            return request;
        }

    }

    public static String hmacDigest(String msg, String keyString) throws Exception {
        String digest = null;
        try {
            SecretKeySpec key = new SecretKeySpec((keyString).getBytes("UTF-8"), "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(key);
            byte[] bytes = mac.doFinal(msg.getBytes("ASCII"));
            StringBuffer hash = new StringBuffer();
            for (int i = 0; i <bytes.length; i++) {
                String hex = Integer.toHexString(0xFF & bytes[i]);
                if (hex.length() == 1) {
                    hash.append('0');
                }
                hash.append(hex);
            }
            digest = hash.toString();
        } catch (UnsupportedEncodingException e) {
            throw new BaseException("Unsupported Encoding Exception",e);
        } catch (InvalidKeyException e) {
            throw new BaseException("Invalid Key Exception",e);
        } catch (NoSuchAlgorithmException e) {
            throw new BaseException("no valid algorithm Exception",e);
        }
        return digest;
    }
    public PayPhiEdcCreateRequest createRequest(OrderPaymentRequest request){
        return PayPhiEdcCreateRequest.builder()
                .aggregatorId(request.getAggregatorId())
                .storeCode(request.getStoreCode())
                .posAppId(request.getPosAppId())
                .posTillNo(request.getPosTillNo())
                .chargeAmount(request.getPaidAmount().toString())
                .currencyCode(request.getCurrencyCode())
                .desc(request.getDesc())
                .build();
    }
    public String createPayPhiEdcRequest(OrderPaymentRequest request){
        Map<String,String> obj =new LinkedHashMap<>();
        obj.put("aggregatorId",request.getAggregatorId());
        obj.put("chargeAmount", request.getPaidAmount().toString());
        obj.put("currencyCode", request.getCurrencyCode());
        obj.put("desc", request.getDesc());
        obj.put("posAppId", request.getPosAppId());
        obj.put("posTillNo", request.getPosTillNo());
        obj.put("referenceNo", ApplicationUtils.getGeneratePartnerExternalOrderId(ApplicationConstant.KETTLE_ORDER, AppConstants.DATE_TIME_FORMATTER_WITH_NO_CHARACTERS) );
        obj.put("storeCode", request.getStoreCode());
        return new Gson().toJson(obj,LinkedHashMap.class);
    }

    public static String stringToHex(String input) {
        StringBuilder hexStringBuilder = new StringBuilder();

        for (char ch : input.toCharArray()) {
            String hexValue = Integer.toHexString(ch);
            hexStringBuilder.append(hexValue);
        }

        return hexStringBuilder.toString();
    }

     public void getOrderPaymentDetail(OrderPaymentRequest orderPaymentRequest,PayPhiEdcCreateResponse payPhiEdcCreateResponse){
         OrderPaymentDetailEntity detail = new OrderPaymentDetailEntity();
         if (orderPaymentRequest.getPaymentModeId() != PaymentPartner.INGENICO.getSystemId(null)) {
             cancelIfExist(payPhiEdcCreateResponse.getReferenceNo());
         }
         detail.setExternalOrderId(payPhiEdcCreateResponse.getReferenceNo());
         detail.setPaymentModeId(orderPaymentRequest.getPaymentModeId());
         detail.setPaymentSource(orderPaymentRequest.getPaymentSource().name());
         detail.setPaymentModeName(orderPaymentRequest.getPaymentModeName());
         detail.setPaymentStatus(payPhiEdcCreateResponse.getResponseCode());
         detail.setRequestStatus(PaymentRequestStatus.INITIATED.name());
         detail.setRequestTime(AppUtils.getCurrentTimestamp());
         detail.setCustomerId(orderPaymentRequest.getCustomerId());
         detail.setCustomerName(orderPaymentRequest.getCustomerName());
         detail.setTransactionAmount(orderPaymentRequest.getPaidAmount());
         detail.setContactNumber(orderPaymentRequest.getContactNumber());
         detail = orderPaymentDetailRepository.save(detail);
         if (Objects.isNull(detail.getOrderPaymentDetailId())) {
             throw new BaseException("Order Payment Request Failed");
         }
    }

    private void cancelIfExist(String merchantTransactionId) {
        List<OrderPaymentDetailEntity> orderPaymentDetailEntities = orderPaymentDetailRepository
                .findByExternalOrderIdAndRequestStatusNot(merchantTransactionId, PaymentRequestStatus.CANCELLED.name());
        if (!orderPaymentDetailEntities.isEmpty()) {
            orderPaymentDetailEntities.forEach(orderPaymentDetailEntity -> {
                orderPaymentDetailEntity.setRequestStatus(PaymentRequestStatus.CANCELLED.name());
                orderPaymentDetailEntity.setUpdateTime(AppUtils.getCurrentTimestamp());
                orderPaymentDetailEntity = orderPaymentDetailRepository.save(orderPaymentDetailEntity);
                if (Objects.isNull(orderPaymentDetailEntity.getOrderPaymentDetailId())) {
                    throw new BaseException("Unable to Cancel Already Present Payment Request");
                }
            });
        }
    }

    @Override
    public PayPhiStatusRequest updatePayment(PayPhiStatusRequest request) throws Exception{

        String msg = createPayPhiStatusRequest(request);
        String hashKey = hmacDigest(msg, payphiProperties.getSecretKey());
        String response = webClientService.postRequestPayPhi(payphiProperties.getStatusApi(),msg, hashKey);
        log.info("Printing value of payphi response::{}",response);
        PayPhiStatusResponse payPhiStatusResponse = ApplicationUtils.parseResponse(response, PayPhiStatusResponse.class);

        if(Objects.isNull(payPhiStatusResponse)){
            throw new BaseException("Server Not Responding");
        }
        OrderPaymentDetailEntity orderPaymentDetail = orderPaymentDetailRepository.findByExternalOrderId(payPhiStatusResponse.getReferenceNo());
        if (Objects.isNull(orderPaymentDetail)) {
            throw new BaseException("No Transaction Found with transaction number"
                    + payPhiStatusResponse.getReferenceNo());
        }
//        if(Objects.nonNull(payPhiStatusResponse.getLoyaltyPointsBurnt()) && Objects.nonNull(payPhiStatusResponse.getPaymentAmount())){
//            orderPaymentDetail.setTransactionAmount(new BigDecimal(payPhiStatusResponse.getPaymentAmount()));
//        }
        orderPaymentDetail.setPartnerTransactionId(payPhiStatusResponse.getTxnID());
        orderPaymentDetail.setPartnerOrderId(payPhiStatusResponse.getTxnAuthID());
        if(Objects.nonNull(payPhiStatusResponse.getInvoiceStatus()) &&
                (payPhiStatusResponse.getInvoiceStatus().equalsIgnoreCase("0000") || payPhiStatusResponse.getInvoiceStatus().equalsIgnoreCase("000"))
        && Objects.nonNull(payPhiStatusResponse.getTxnResponseCode()) && (payPhiStatusResponse.getTxnResponseCode().equalsIgnoreCase("0000") || payPhiStatusResponse.getTxnResponseCode().equalsIgnoreCase("000"))) {
            orderPaymentDetail.setPartnerPaymentStatus("ACCEPTED_SUCCESS");
        }
        else{
            orderPaymentDetail.setPartnerPaymentStatus("FAILED");
        }
        orderPaymentDetail.setResponseTime(ApplicationUtils.getCurrentTimestamp());
        orderPaymentDetail = orderPaymentDetailRepository.save(orderPaymentDetail);
        if (Objects.isNull(orderPaymentDetail.getOrderPaymentDetailId())) {
            throw new BaseException("Order Payment Request Updation Failed");
        }

        request.setPayPhiStatusResponse(payPhiStatusResponse);
        return request;
    }

    public String createPayPhiStatusRequest(PayPhiStatusRequest request){
        Map<String, String> map = new LinkedHashMap<>();
        map.put("aggregatorId",request.getAggregatorId());
        map.put("invoiceNo",request.getInvoiceNo());
        map.put("posAppId",request.getPosAppId());
        map.put("referenceNo",request.getReferenceNo());
        map.put("storeCode",request.getStoreCode());
        map.put("transactionType",request.getTransactionType());
        return new Gson().toJson(map,LinkedHashMap.class);
    }

}
