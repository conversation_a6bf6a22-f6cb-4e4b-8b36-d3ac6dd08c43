package com.stpl.tech.pivot.service;

import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import jakarta.servlet.http.HttpServletRequest;

import java.util.Map;

public interface AuthenticationService {

    void validateSession(int unitId, int userId, String sessionKey) throws AuthenticationFailureException;

    void addRequestId(HttpServletRequest request);

    void clearAclURICache();	}
