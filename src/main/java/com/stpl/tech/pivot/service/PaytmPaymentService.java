package com.stpl.tech.pivot.service;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.pivot.domain.PaytmEdcCreateRequest;
import com.stpl.tech.pivot.domain.PaytmEdcStatusRequest;

public interface PaytmPaymentService {
    PaytmEdcCreateRequest initiateEdcTransaction(OrderPaymentRequest orderPaymentRequest) throws Exception;

    Object updatePayment(PaytmEdcStatusRequest request, boolean skipSignatureVerification) throws Exception;
}
