/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.pivot.interceptors;


import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.external.interceptor.ExternalAPITokenCache;
import com.stpl.tech.pivot.properties.EnvironmentProperties;
import com.stpl.tech.pivot.service.ACLService;
import com.stpl.tech.pivot.service.AuthenticationService;
import com.stpl.tech.pivot.service.TokenService;
import com.stpl.tech.pivot.service.impl.JWTToken;
import com.stpl.tech.util.ACLUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.net.URL;
import java.util.Objects;

@Component
@Log4j2
public class AuthInterceptor implements HandlerInterceptor {

//	@Autowired
//	private TokenService<JWTToken> jwtService;

	@Autowired
	private TokenService<JWTToken> jwtService;

//	@Autowired
//	private com.stpl.tech.pivot.service.TokenService<com.stpl.tech.pivot.service.impl.JWTToken>

	@Autowired
	private AuthenticationService authenticationService;

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private ACLService aclService;

	@Autowired
	private ExternalAPITokenCache apiTokenCache;

	@Override
	public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o)
			throws Exception {
		if (httpServletRequest.getMethod().equals("OPTIONS")) {
			URL originUrl = new URL(httpServletRequest.getHeader("origin"));
			if (httpServletRequest.getHeader("origin").toString().contains("localhost")) {
				return true;
			}
			if (originUrl.getHost().equals(httpServletRequest.getHeader("host").split(":")[0])) {
				return true;
			}
			return false;
		}
		ACLUtil aclUtil = ACLUtil.getInstance();
		authenticationService.addRequestId(httpServletRequest);
		boolean validate = properties.getRunValidateFilter();
		String module = aclUtil.convertURIToModule(httpServletRequest.getRequestURI());
		if (!validate || aclService.isPreAuthenticated(module)) {
			return true;
		}
		String authHeader = Objects.nonNull(httpServletRequest.getHeader("auth"))
				? httpServletRequest.getHeader("auth").trim()
				: httpServletRequest.getHeader("auth");
		String authInternalHeader = null;
		String accessKey = null;
		if (StringUtils.isNotBlank(httpServletRequest.getHeader("auth-internal"))) {
			authInternalHeader = httpServletRequest.getHeader("auth-internal").trim();
		} else if (StringUtils.isNotBlank(httpServletRequest.getHeader("Authorisation"))) {
			authInternalHeader = httpServletRequest.getHeader("auth-internal").trim();
		} else if (StringUtils.isNotEmpty(httpServletRequest.getParameter("accessKey"))) {
			accessKey = httpServletRequest.getParameter("accessKey").trim();
		}
		if (StringUtils.isNotBlank(authHeader) && !authHeader.equals("null")) {
			JWTToken jwtToken = new JWTToken();
			jwtService.parseToken(jwtToken, authHeader);
			String sessionKey = jwtToken.getSessionKey();
			int terminalId = jwtToken.getTerminalId();
			int userId = jwtToken.getUserId();
			int unitId = jwtToken.getUnitId();
			try {
				authenticationService.validateSession(unitId, userId, sessionKey);
				return true;
			} catch (AuthenticationFailureException e) {
				String message = String.format(
						"Session expired (CHAAYOS): Unit Id %d , Terminal Id %d, User Id %d, Session key Id %s", unitId,
						terminalId, userId, sessionKey);
				throw new AuthenticationFailureException(message, e);
			}
		} else if (StringUtils.isNotBlank(authInternalHeader) && !authInternalHeader.equals("null")) {
			if (apiTokenCache.isValidKey(authInternalHeader) && apiTokenCache.checkAccess(authInternalHeader,
					httpServletRequest.getRequestURI(), httpServletRequest.getMethod())) {
				return true;
			} else {
				throw new AuthenticationFailureException(
						String.format("Invalid tokens in header : %s", authInternalHeader));
			}
		} else if (StringUtils.isNotBlank(accessKey) && !accessKey.equals("null")) {
			if (apiTokenCache.isValidKey(accessKey) && apiTokenCache.checkAccess(accessKey,
					httpServletRequest.getRequestURI(), httpServletRequest.getMethod())) {
				return true;
			} else {
				throw new AuthenticationFailureException(String.format("Invalid tokens in header : %s", accessKey));
			}
		} else {
			log.info("Session expired: Invalid tokens in header");
			throw new AuthenticationFailureException("Session expired: Invalid tokens in header");
		}

	}

	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {
		MDC.clear();
	}

}
